<template>
	<div class="bottom" data-css="lazyload" data-original="/media/images/footer-new.jpg">
		<div class="df wrapper wrapper-bottom">
			<div class="bottom-col bottom-col1" :class="{'active': bottomCol1}">
				<div class="bottom-title" @click="bottomCol1 = !bottomCol1"><BaseCmsLabel code="footer_info" /> <span class="toggle-icon" v-if="mobileBreakpoint"></span></div>
				<div class="bottom-col-cnt">
					<BaseCmsNav code="footer_col1" v-slot="{items, currentUrl}">
						<ul class="nav-bottom" v-if="items?.length">
							<li v-for="item in items" :key="item.id" :class="{'selected': currentUrl == item.url_without_domain}">
								<NuxtLink :to="item.url_without_domain">{{ item.title }}</NuxtLink>
							</li>
						</ul>
					</BaseCmsNav>
				</div>
			</div>
			<div class="bottom-col bottom-col2" :class="{'active': bottomCol2}">
				<div class="bottom-title" @click="bottomCol2 = !bottomCol2"><BaseCmsLabel code="footer_terms" tag="span" /> <span class="toggle-icon" v-if="mobileBreakpoint"></span></div>
				<div class="bottom-col-cnt">
					<BaseCmsNav code="footer_col2" v-slot="{items, currentUrl}">
						<ul class="nav-bottom" v-if="items?.length">
							<li v-for="item in items" :key="item.id" :class="{'selected': currentUrl == item.url_without_domain}">
								<NuxtLink :to="item.url_without_domain">{{ item.title }}</NuxtLink>
							</li>
							<li>
								<a class="gdpr_configurator_button" href="javascript:void(0);"><BaseCmsLabel code="gdpr_edit" /></a>
							</li>
						</ul>
					</BaseCmsNav>
				</div>
			</div>
			<div class="bottom-col bottom-col3" :class="{'active': bottomCol3}">
				<div class="bottom-title" @click="bottomCol3 = !bottomCol3"><BaseCmsLabel code="footer_col3_title" tag="span" /> <span class="toggle-icon" v-if="mobileBreakpoint"></span></div>
				<BaseCmsLabel code="footer_payment" tag="div" class="bottom-col-cnt" />
			</div>
			<div class="bottom-col bottom-col4" :class="{'active': bottomCol4}">
				<div class="bottom-title" @click="bottomCol4 = !bottomCol4"><BaseCmsLabel code="footer_bussines_time_title" tag="span" /> <span class="toggle-icon" v-if="mobileBreakpoint"></span></div>
				<div class="bottom-col-cnt">
					<BaseLocationPoints v-slot="{items}">
						<ul class="bottom-locations" v-if="items?.length">
							<li v-for="item in items" :key="item.id">
								<NuxtLink :to="item.url_without_domain">
									<template v-if="item?.headline">{{item.headline}}</template>
									<template v-else>{{item.title}}</template>
								</NuxtLink>
							</li>
						</ul>
					</BaseLocationPoints>
				</div>
			</div>
		</div>
	</div>
</template>

<script setup>
	const {mobileBreakpoint} = inject('rwd');
	const bottomCol1 = ref(false);
	const bottomCol2 = ref(false);
	const bottomCol3 = ref(false);
	const bottomCol4 = ref(false);
</script>
