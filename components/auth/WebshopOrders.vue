<template>
	<template v-if="orders?.length">
		<div class="w-table w-table-head">
			<BaseCmsLabel code="webshop_order_number" class="w-table-col col-num" tag="div" />
			<BaseCmsLabel code="date" class="w-table-col col-date" tag="div" />
			<BaseCmsLabel code="amount" class="w-table-col col-total" tag="div" />
			<BaseCmsLabel code="status" class="w-table-col col-status" tag="div" />
			<div class="w-table-col col-btns"></div>
		</div>
		<div class="orders" id="items_widgetlist_webshop">
			<BaseUiAccordion>
				<BaseUiAccordionPanel v-for="item in visibleOrders" :key="item.id" :id="item.id" v-slot="{onToggle, active}">
					<div class="order-row" :class="{'active': active, 'active': mode == 'dashboard'}" :id="`order-${item.number}`">
						<!-- Order Item -->
						<div class="w-table" @click="onToggle">
							<div class="w-table-col col-num"><BaseCmsLabel class="w-table-label" tag="span" code="webshop_order_number" /> {{item.id}}</div>
							<div class="w-table-col col-date"><BaseCmsLabel class="w-table-label" tag="span" code="date" /><BaseUtilsFormatDate :date="item.datetime_created" /></div>
							<div class="w-table-col col-total">
								<span class="w-table-label"><BaseCmsLabel code="amount" />:</span>
								<span><BaseUtilsFormatCurrency :price="item.total" /></span>
							</div>
							<div class="w-table-col col-status col-td-status">
								{{item.status.title}}
								<div class="order-status">
									<span class="order-status-bar" :style="getStatusStyle(item.status)"></span>
								</div>
							</div>
							<div class="w-table-col col-btns" v-if="mode != 'dashboard'">
								<div class="btn-order-details" :class="{'active': active}" id="btn-order">
									<span class="btn-inactive" v-if="!active"><BaseCmsLabel code="webshop_btn_order_details" /></span>
									<span class="btn-active" v-else><BaseCmsLabel code="webshop_btn_hide_order_details" /></span>
									<span class="toggle-icon"></span>
								</div>
							</div>
						</div>
						<div v-if="item.order_items" v-show="mode !== 'dashboard' ? active : true" class="order-details">
							<div class="w-table w-table-details">
								<AuthOrderItem v-for="product in item.order_items" :product="product" :key="product.id" />
							</div>
							<!-- FIXME - ako zatreba kod upitnika zadovoljstva da imamo <NuxtLink v-if="item?.satisfaction_survey_allowed && item?.satisfaction_survey_url" :to="item.satisfaction_survey_url" class="btn btn-primary btn-satisfaction">
								<span><BaseCmsLabel code="profile_satisfaction_btn" /></span>
							</NuxtLink> -->
							<div class="wp-total-sum">
								<div class="wp-total-row">
									<BaseCmsLabel code="payment" tag="span" class="label" />:
									<span class="value">{{item.payment_titles}}</span>
								</div>
								<div class="wp-total-row">
									<BaseCmsLabel code="shipping" tag="span" class="label" />:
									<span class="value">
										{{item.delivery_titles}}
										<strong>(<BaseUtilsFormatCurrency :price="item.shipping_total" />)</strong>
									</span>
								</div>
								<div class="wp-total-row" v-if="item.loyalty_discount_percent > 0">
									<div class="wp-total-row">
										<BaseCmsLabel code="loyalty_discount" tag="span" class="label" />:
										<span class="value">
											<strong>{{ getRoundedLoyaltyDiscount(item) }}%</strong>
										</span>
									</div>
								</div>
								<div class="wp-total-row wp-total-row-total">
									<BaseCmsLabel code="total" tag="span" class="label" />:
									<span class="value"><BaseUtilsFormatCurrency :price="item.total" /></span>
								</div>
								<!-- <div class="total-terms" v-if="item.terms_pdf">
									<a :href="item.terms_pdf_url" target="_blank"><BaseCmsLabel code="thank_you_download_terms_pdf" /></a>
								</div> -->
							</div>
						</div>
					</div>
				</BaseUiAccordionPanel>
			</BaseUiAccordion>
		</div>
	</template>
</template>

<script setup>
	const props = defineProps(['orders', 'mode', 'item']);
	const visibleOrders = computed(() => {
		return props.mode === 'dashboard' ? props.orders.slice(0, 1) : props.orders;
	});

	function getRoundedLoyaltyDiscount(item) {
		return Math.round(item.loyalty_discount_percent);
	}

	function getStatusStyle(status) {
		let width = '30%';
		let color = '#ABC075';

		if (status.id === '2') {
			width = '40%';
		}
		if (status.id === '3') {
			width = '55%';
		}
		if (status.id === '4') {
			width = '75%';
		}
		if (status.id === '5' || status.id === '6' || status.id === '7') {
			width = '100%';
		}

		return `width: ${width}; background: ${color};`;
	}
</script>
