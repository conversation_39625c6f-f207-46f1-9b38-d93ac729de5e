<template>
	<BaseWebshopCartErrors v-slot="{errors}">
		<div class="wp-pickup-products" v-if="hasPickupProducts">
			<BaseCmsLabel class="status-info-label" code="pickup_info" tag="div" />
			<BaseCmsLabel class="wp-pickup-products-info" code="cart_availability_onlyinoffice" tag="div" />
			<BaseCmsLabel class="wp-pickup-select-label" code="select_store" tag="span" />
			<div v-if="errors?.length && errors[0]?.label_name === 'error_location_point_required'">
				<div class="error global-error" v-for="error in errors" :key="error"><BaseCmsLabel :code="error.label_name" /></div>
			</div>
			<select v-if="locations?.length" @change="submitLocation" id="field-shipping_pickup_location">
				<option value=""><BaseCmsLabel code="select_store_label" /></option>
				<option v-for="location in locations" :key="location.id" :value="location.id" :selected="location.id == selectedLocation?.id">{{ location.title }}</option>
			</select>
			<BaseCmsLabel code="" v-else />
		</div>
		<WebshopCartItem v-for="item in parcel.items" :data="item" :key="item.shopping_cart_code" :pickupErrors="errors" mode="cartAll" />
		<div class="wp-notavailable-for-pickup-products" v-if="errors?.length && errors[0]?.label_name === 'error_location_point_required' && hasPickupProducts">
			<div class="wp-pickup-notavailable">
				<span v-html="labels.get('unable_to_complete_order').replace('%TOTAL_PRODUCTS%', `${parcel.items.filter(item => item.type === 'pickup').length}`)"></span>
			</div>
			<BaseWebshopRemoveProduct :item="parcel.items.filter(item => item.type === 'pickup')" v-slot="{onRemove, loading}">
				<!-- FIXME - potrebno je riješiti da kada se obrišu proizvodi koji imaju pickup u košarici da se updejta i shipping, također to je potrebno napraviti unutar WebshopCartItem komponente na wp-btn-delete kod pickupa 
							 Brisanje radi, međutim ako obrišemo a nismo promijenili shipping lokacije svejedno ostane cart.errors jer funkcioniora samo na submitLocation
				-->
				<button class="wp-btn wp-btn-delete wp-pickup-btn-delete" @click="onRemove" :class="{'loading': loading}">
					<UiLoader class="small" v-if="loading" />
					<BaseCmsLabel code="remove_products" tag="span" />
				</button>
				<WebshopCartItem v-for="item in parcel.items.filter(item => item.type === 'pickup')" :data="item" :key="item.shopping_cart_code" />
			</BaseWebshopRemoveProduct>
		</div>
	</BaseWebshopCartErrors>
</template>

<script setup>
	const labels = useLabels();
	const {getCartData, updateShipping} = useWebshop();
	const parcel = computed(() => getCartData()?.parcels[0]);

	const hasPickupProducts = computed(() => {
		const cartProducts = parcel.value?.items;
		if (!cartProducts?.length) return false;
		return cartProducts.some(item => item.type == 'pickup');
	});
	const shoppingCartCodes = computed(() => {
		return parcel.value.items.map(item => item.shopping_cart_code);
	});
	const locations = computed(() => parcel.value?.shipping?.pickup_location?.available || []);
	const selectedLocation = computed(() => parcel.value?.shipping?.pickup_location?.selected || null);
	const loading = ref(false);
	const status = ref(null);

	async function submitLocation(event) {
		loading.value = true;
		status.value = await updateShipping({
			shipping_id: parcel.value.shipping?.selected?.id,
			shopping_cart_codes: shoppingCartCodes.value,
			shipping_data: {
				location_point_id: event.target.value,
			},
		});
		loading.value = false;
	}
</script>
